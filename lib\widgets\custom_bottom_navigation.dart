import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class CustomBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50, // Further reduced height for even tighter spacing
      decoration: const BoxDecoration(
        color: AppColors.darkBlue,
        border: Border(
          top: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
        ),
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 4.0,
          ), // Further reduced padding
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNavItem(icon: Icons.home, index: 0, size: 28),
              _buildNavItem(icon: Icons.add_circle_outline, index: 1, size: 32),
              _buildNavItem(icon: Icons.person, index: 2, size: 28),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required int index,
    required double size,
  }) {
    final isSelected = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Icon(
          icon,
          size: size,
          color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
        ),
      ),
    );
  }
}
