import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../widgets/common/gf_button.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({Key? key}) : super(key: key);

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Upload Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [AppColors.lightTeal, AppColors.teal],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  border: Border.all(color: AppColors.gfGreen, width: 3),
                ),
                child: const Icon(
                  Icons.add_a_photo,
                  size: 60,
                  color: AppColors.darkBlue,
                ),
              ),

              const SizedBox(height: 32),

              // Title
              const Text(
                'Create Post',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AppColors.gfOffWhite,
                ),
              ),

              const SizedBox(height: 16),

              // Subtitle
              const Text(
                'Share your gaming moments with the community',
                style: TextStyle(
                  fontSize: 16,
                  color: AppColors.gfGrayText,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 48),

              // Upload Options
              Column(
                children: [
                  // Take Photo Button
                  SizedBox(
                    width: double.infinity,
                    child: GFButton(
                      text: 'Take Photo',
                      onPressed: () {
                        _showComingSoonSnackBar('Take Photo');
                      },
                      type: GFButtonType.primary,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Choose from Gallery Button
                  SizedBox(
                    width: double.infinity,
                    child: GFButton(
                      text: 'Choose from Gallery',
                      onPressed: () {
                        _showComingSoonSnackBar('Choose from Gallery');
                      },
                      type: GFButtonType.secondary,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Record Video Button
                  SizedBox(
                    width: double.infinity,
                    child: GFButton(
                      text: 'Record Video',
                      onPressed: () {
                        _showComingSoonSnackBar('Record Video');
                      },
                      type: GFButtonType.secondary,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Choose Video Button
                  SizedBox(
                    width: double.infinity,
                    child: GFButton(
                      text: 'Choose Video',
                      onPressed: () {
                        _showComingSoonSnackBar('Choose Video');
                      },
                      type: GFButtonType.secondary,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Info Text
              const Text(
                'Supported formats: JPG, PNG, MP4, MOV',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.gfGrayText,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showComingSoonSnackBar(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
