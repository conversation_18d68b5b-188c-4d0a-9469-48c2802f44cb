import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({super.key});

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Link Gaming Accounts Section
                    _buildLinkGamingAccountsSection(),

                    const SizedBox(height: 32),

                    // Linked Libraries Section
                    _buildLinkedLibrariesSection(),

                    const SizedBox(height: 32),

                    // Phone Library Section
                    _buildPhoneLibrarySection(),

                    const SizedBox(height: 48),

                    // App Logo
                    _buildAppLogo(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const SizedBox(width: 40), // Spacer for centering
          const Text(
            'Select Source',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.gfOffWhite,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: AppColors.gfOffWhite,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLinkGamingAccountsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Link Gaming Accounts',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.gfOffWhite,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Connect your gaming accounts to access and share content from your game libraries. Upload your best gaming moments with ease.',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.gfGrayText,
            height: 1.4,
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildPlatformCard('Xbox', Icons.videogame_asset, () {
              _showComingSoonSnackBar('Xbox integration');
            }),
            _buildPlatformCard('PlayStation', Icons.sports_esports, () {
              _showComingSoonSnackBar('PlayStation integration');
            }),
            _buildPlatformCard('Steam', Icons.computer, () {
              _showComingSoonSnackBar('Steam integration');
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildPlatformCard(String name, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.gfGrayBorder, width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: AppColors.gfGrayText),
            const SizedBox(height: 4),
            Text(
              name,
              style: const TextStyle(
                fontSize: 10,
                color: AppColors.gfGrayText,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkedLibrariesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Linked Libraries',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.gfOffWhite,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Access all your linked game libraries and screenshots.',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.gfGrayText,
            height: 1.4,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.gfCardBackground.withValues(
              alpha: 128,
            ), // 0.5 opacity
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.gfGrayBorder, width: 1),
          ),
          child: const Center(
            child: Text(
              'No linked accounts yet\nConnect your gaming platforms above',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.gfGrayText,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneLibrarySection() {
    return GestureDetector(
      onTap: () {
        _showComingSoonSnackBar('Phone Library access');
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.gfCardBackground,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.gfGrayBorder, width: 1),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.gfDarkBackground40,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.photo_library,
                color: AppColors.gfOffWhite,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Text(
                'Phone Library',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.gfOffWhite,
                ),
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.gfGrayText,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppLogo() {
    return Center(
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: const LinearGradient(
            colors: [AppColors.lightTeal, AppColors.teal],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          border: Border.all(color: AppColors.gfGreen, width: 2),
        ),
        child: const Center(
          child: Text(
            'GF',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.darkBlue,
            ),
          ),
        ),
      ),
    );
  }

  void _showComingSoonSnackBar(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
